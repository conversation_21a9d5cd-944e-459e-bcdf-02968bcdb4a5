using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    public sealed class PluginFilter : IFunctionInvocationFilter
    {
        protected readonly IHttpContextAccessor _httpContextAccessor;
        protected CancellationToken cancellationToken = default;
        private readonly AgentSkillEventHelper _eventHelper;
        private readonly ILogger<PluginFilter> _logger;
        private string _processedResultString;

        public PluginFilter(IHttpContextAccessor httpContextAccessor, ILogger<PluginFilter> logger = null)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _eventHelper = new AgentSkillEventHelper(_httpContextAccessor);
            _logger = logger;
        }

        public async Task OnFunctionInvocationAsync(FunctionInvocationContext context, Func<FunctionInvocationContext, Task> next)
        {
            string content = $"调用工具 - {context.Function.PluginName}.{context.Function.Name}";
            Console.WriteLine(content);
            Console.WriteLine($"[OnFunctionInvocationAsync] 开始调用工具: {context.Function.PluginName}.{context.Function.Name}");

            // 提取工具参数信息
            string arguments = "";
            try
            {
                if (context.Arguments != null && context.Arguments.Count > 0)
                {
                    var argDict = new Dictionary<string, object>();
                    foreach (var arg in context.Arguments)
                    {
                        argDict[arg.Key] = arg.Value;
                    }
                    arguments = JsonConvert.SerializeObject(argDict);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OnFunctionInvocationAsync] 提取工具参数时发生错误: {ex.Message}");
                arguments = "{}";
            }

            // 执行工具函数
            await next(context);

            Console.WriteLine($"FunctionInvoked - {context.Function.PluginName}.{context.Function.Name}");
            Console.WriteLine($"[OnFunctionInvocationAsync] 工具执行完成: {context.Function.PluginName}.{context.Function.Name}");

            // 发送ToolEvent事件
            try
            {
                string defaultTitle = $"{context.Function.PluginName}.{context.Function.Name}";
                string title = defaultTitle;
                string name = context.Function.Name;
                string result = context.Result?.GetValue<object>()?.ToString() ?? "";

                // 尝试从HttpContext中获取工具标题映射
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.Items.TryGetValue("ToolTitleMapping", out var mappingObj) == true)
                {
                    if (mappingObj is ConcurrentDictionary<string, string> toolTitleMapping)
                    {
                        // 尝试获取映射的标题
                        if (toolTitleMapping.TryGetValue(defaultTitle, out var mappedTitle))
                        {
                            title = mappedTitle;
                            Console.WriteLine($"[OnFunctionInvocationAsync] 使用映射标题: {defaultTitle} -> {title}");
                        }
                        else
                        {
                            Console.WriteLine($"[OnFunctionInvocationAsync] 未找到映射标题，使用默认标题: {defaultTitle}");
                        }
                    }
                }

                Console.WriteLine($"[OnFunctionInvocationAsync] 发送ToolEvent: title={title}, name={name}");
                await _eventHelper.ToolEvent(title, name, arguments, result, cancellationToken);
                Console.WriteLine($"[OnFunctionInvocationAsync] ToolEvent发送完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OnFunctionInvocationAsync] 发送ToolEvent时发生错误: {ex.Message}");
            }

            // 拦截工具调用结果并处理
            Console.WriteLine($"[OnFunctionInvocationAsync] 准备调用ProcessFunctionResult: {context.Function.PluginName}.{context.Function.Name}");
            await ProcessFunctionResult(context);
            Console.WriteLine($"[OnFunctionInvocationAsync] ProcessFunctionResult调用完成: {context.Function.PluginName}.{context.Function.Name}");
        }

        /// <summary>
        /// 处理工具调用结果，提取source_title和source_url并发送DataEvent
        /// </summary>
        /// <param name="context">函数调用上下文</param>
        private async Task ProcessFunctionResult(FunctionInvocationContext context)
        {
            Console.WriteLine($"[ProcessFunctionResult] 开始处理工具结果: {context.Function.PluginName}.{context.Function.Name}");

            try
            {

                // 获取结果值
                var resultValue = context.Result.GetValue<object>();
                if (resultValue == null)
                {
                    Console.WriteLine($"[ProcessFunctionResult] resultValue为null，跳过处理: {context.Function.PluginName}.{context.Function.Name}");
                    return;
                }

                var resultString = resultValue.ToString();
                if (string.IsNullOrEmpty(resultString))
                {
                    Console.WriteLine($"[ProcessFunctionResult] resultString为空，跳过处理: {context.Function.PluginName}.{context.Function.Name}");
                    return;
                }

                Console.WriteLine($"[ProcessFunctionResult] 处理工具 {context.Function.PluginName}.{context.Function.Name} 的返回结果: {resultString}");

                // 处理不同格式的返回结果
                await ProcessSourceAndQuestions(resultString);

                // 更新context.Result
                if (_processedResultString != resultValue.ToString())
                {
                    context.Result = new FunctionResult(context.Result.Function, _processedResultString, context.Result.Culture, context.Result.Metadata);
                    Console.WriteLine($"[ProcessFunctionResult] 已更新context.Result");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProcessFunctionResult] 处理工具 {context.Function.PluginName}.{context.Function.Name} 返回结果时发生错误: {ex.Message}");
                Console.WriteLine($"[ProcessFunctionResult] 异常堆栈: {ex.StackTrace}");
            }
            finally
            {
                Console.WriteLine($"[ProcessFunctionResult] 完成处理工具结果: {context.Function.PluginName}.{context.Function.Name}");
            }
        }

        /// <summary>
        /// 从JObject中移除指定的属性（支持嵌套的data对象）
        /// </summary>
        /// <param name="jsonObject">JObject对象</param>
        /// <param name="propertyName">要移除的属性名</param>
        private void RemoveJsonProperty(JObject jsonObject, string propertyName)
        {
            try
            {
                bool removed = false;

                // 优先尝试从data对象中移除（新格式）
                var dataObject = jsonObject["data"] as JObject;
                if (dataObject != null && dataObject[propertyName] != null)
                {
                    dataObject.Remove(propertyName);
                    Console.WriteLine($"[RemoveJsonProperty] 从data对象中移除属性: {propertyName}");
                    removed = true;
                }

                // 如果data对象中没有，再尝试从根级别移除（兼容旧格式）
                if (!removed && jsonObject[propertyName] != null)
                {
                    jsonObject.Remove(propertyName);
                    Console.WriteLine($"[RemoveJsonProperty] 从根级别移除属性: {propertyName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RemoveJsonProperty] 移除属性 {propertyName} 时发生错误: {ex.Message}");
            }
        }



        /// <summary>
        /// 处理不同格式的返回结果中的source和questions信息
        /// </summary>
        /// <param name="resultString">原始结果字符串</param>
        /// <returns></returns>
        private async Task ProcessSourceAndQuestions(string resultString)
        {
            _processedResultString = resultString;

            Console.WriteLine($"[ProcessSourceAndQuestions] 开始处理结果");

            try
            {
                // 解析JSON
                var jsonObject = JObject.Parse(resultString);

                // 检测结果格式并提取相关信息
                string sourceTitle = null;
                string sourceUrl = null;
                JArray questionsArray = null;

                // 优先从data字段中提取（新格式）
                var dataObject = jsonObject["data"] as JObject;
                if (dataObject != null)
                {
                    Console.WriteLine($"[ProcessSourceAndQuestions] 找到data字段，优先从data字段提取");

                    sourceTitle = dataObject["source_title"]?.ToString();
                    sourceUrl = dataObject["source_url"]?.ToString();
                    questionsArray = dataObject["questions"] as JArray;

                    Console.WriteLine($"[ProcessSourceAndQuestions] 从data字段提取结果: sourceTitle={sourceTitle}, sourceUrl={sourceUrl}, questions={questionsArray?.Count ?? 0}个");
                }

                // 如果data字段中没有找到，再尝试从根级别提取（兼容旧格式）
                if (string.IsNullOrEmpty(sourceTitle) && string.IsNullOrEmpty(sourceUrl) && questionsArray == null)
                {
                    Console.WriteLine($"[ProcessSourceAndQuestions] data字段未找到，尝试从根级别提取");

                    sourceTitle = jsonObject["source_title"]?.ToString();
                    sourceUrl = jsonObject["source_url"]?.ToString();
                    questionsArray = jsonObject["questions"] as JArray;

                    Console.WriteLine($"[ProcessSourceAndQuestions] 从根级别提取结果: sourceTitle={sourceTitle}, sourceUrl={sourceUrl}, questions={questionsArray?.Count ?? 0}个");
                }

                // 处理source信息
                if (!string.IsNullOrEmpty(sourceTitle) && !string.IsNullOrEmpty(sourceUrl))
                {
                    Console.WriteLine($"[ProcessSourceAndQuestions] 发现source信息: title={sourceTitle}, url={sourceUrl}");

                    // 发送DataEvent到前端
                    var sourceDataEvent = new
                    {
                        type = "source",
                        data = new[]
                        {
                            new
                            {
                                type = "preview",
                                name = sourceTitle,
                                url = sourceUrl
                            }
                        }
                    };

                    var dataEventContent = JsonConvert.SerializeObject(sourceDataEvent);
                    await _eventHelper.DataEvent(dataEventContent, cancellationToken);

                    Console.WriteLine($"[ProcessSourceAndQuestions] 已发送source DataEvent");

                    // 从结果中移除source信息
                    RemoveJsonProperty(jsonObject, "source_title");
                    RemoveJsonProperty(jsonObject, "source_url");
                }
                else
                {
                    Console.WriteLine($"[ProcessSourceAndQuestions] 未找到source信息");
                }

                // 处理questions信息
                if (questionsArray != null && questionsArray.Count > 0)
                {
                    Console.WriteLine($"[ProcessSourceAndQuestions] 发现questions信息: {questionsArray}");

                    // 发送DataEvent到前端 - type为recommend，data为整个questions数组
                    var questionsDataEvent = new
                    {
                        type = "recommend",
                        data = questionsArray
                    };

                    var dataEventContent = JsonConvert.SerializeObject(questionsDataEvent);
                    await _eventHelper.DataEvent(dataEventContent, cancellationToken);

                    Console.WriteLine($"[ProcessSourceAndQuestions] 已发送questions DataEvent: {questionsArray}");

                    // 从结果中移除questions
                    RemoveJsonProperty(jsonObject, "questions");
                }
                else
                {
                    Console.WriteLine($"[ProcessSourceAndQuestions] 未找到questions信息");
                }

                // 更新处理后的结果
                _processedResultString = jsonObject.ToString(Formatting.None);
                Console.WriteLine($"[ProcessSourceAndQuestions] 处理完成，最终结果: {_processedResultString}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProcessSourceAndQuestions] JSON解析失败: {ex.Message}");
                // 如果JSON解析失败，保持原始结果不变
                _processedResultString = resultString;
            }
        }


    }
}