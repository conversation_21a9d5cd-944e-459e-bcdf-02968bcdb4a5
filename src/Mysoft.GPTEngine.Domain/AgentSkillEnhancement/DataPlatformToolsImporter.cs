using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Shared;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    public class DataPlatformToolsImporter
    {
        private readonly Kernel _kernel;
        private readonly ILogger<DataPlatformToolsImporter> _logger;
        private readonly IMysoftApiService _apiService;
        private readonly IConfigurationService _configurationService;
        private readonly AgentSkillEventHelper _eventHelper;

        public DataPlatformToolsImporter(Kernel kernel, IServiceProvider serviceProvider)
        {
            _kernel = kernel;
            _logger = serviceProvider.GetService<ILoggerFactory>().CreateLogger<DataPlatformToolsImporter>();
            _apiService = serviceProvider.GetService<IMysoftApiService>();
            _configurationService = serviceProvider.GetService<IConfigurationService>();
            _eventHelper = new AgentSkillEventHelper(serviceProvider.GetService<IHttpContextAccessor>());
        }

        public async Task ImportGetDataTools(ConcurrentDictionary<string, string> toolTitleMapping = null)
        {
            var tools = await GetToolList();
            if (tools == null || !tools.Any())
            {
                await _eventHelper.TextEvent("[未获取到任何取数工具]", CancellationToken.None);
                return;
            }

            var functions = new List<KernelFunction>();
            foreach (var tool in tools)
            {
                string id = tool.id;
                string name = tool.name;
                string title = tool.title;
                string description = tool.description;
                var inputSchema = tool.input_schema;

                var parameters = new List<KernelParameterMetadata>();
                foreach (var prop in inputSchema.properties)
                {
                    parameters.Add(new KernelParameterMetadata(prop.Name)
                    {
                        Description = prop.Value.description != null ? prop.Value.description.ToString() : ""
                    });
                }

                // 确保工具名称不超过64个字符（考虑插件名长度）
                var truncatedName = ToolNameHelper.TruncateFunctionName(name, "GetDataTools");
                if (truncatedName != name)
                {
                    _logger.LogWarning("[ImportGetDataTools] 工具名称过长，已截断: {originalName} -> {truncatedName}", name, truncatedName);
                }

                functions.Add(KernelFunctionFactory.CreateFromMethod(
                    method: (Func<KernelArguments, CancellationToken, Task<string>>)GetDataFunc,
                    functionName: truncatedName,
                    description: description,
                    parameters: parameters
                ));

                // 添加工具标题映射，使用tool.title作为显示标题
                if (toolTitleMapping != null)
                {
                    string toolKey = $"GetDataTools.{truncatedName}";
                    string displayTitle = !string.IsNullOrEmpty(title) ? title : name;
                    toolTitleMapping.TryAdd(toolKey, displayTitle);
                    _logger.LogInformation("[ImportGetDataTools] 添加数据平台工具标题映射: {toolKey} -> {title}", toolKey, displayTitle);
                }

                continue;

                async Task<string> GetDataFunc(KernelArguments arguments, CancellationToken ct)
                {
                    var dict = new Dictionary<string, string>();
                    foreach (var prop in inputSchema.properties)
                    {
                        string key = prop.Name;
                        var value = arguments.TryGetValue(key, out var v) ? v?.ToString() : "";

                        if (!string.IsNullOrEmpty(value))
                            dict[key] = value;
                    }

                    return await GetData(id, name, title, JsonConvert.SerializeObject(dict), ct);
                }
            }

            var plugin = KernelPluginFactory.CreateFromFunctions("GetDataTools", null, functions);
            _kernel.Plugins.Add(plugin);
        }

        private async Task<dynamic[]> GetToolList()
        {
            var dpUrl = _configurationService.GetConfigurationItemByKey(EMCConfigConst.DpUrl).TrimEnd('/');
            var url = $"{dpUrl}/openapi/ai/get_tool_list";

            try
            {
                var response = await _apiService.PostAsync(url, "");
                var result = JsonConvert.DeserializeObject<dynamic>(response);
                if (result != null && result.success == true && result.data != null)
                {
                    var tools = ((JArray)result.data).ToArray<dynamic>();
                    return tools;
                }

                _logger.LogError("GetToolList接口失败. 响应: {response}", response);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "GetToolList接口异常");
            }

            return null;
        }



        private async Task<string> GetData(
            string id,
            string name,
            string title,
            string @params,
            CancellationToken cancellationToken = default)
        {
            var dpUrl = _configurationService.GetConfigurationItemByKey(EMCConfigConst.DpUrl).TrimEnd('/');
            var url = $"{dpUrl}/openapi/ai/get_data";

            try
            {
                var requestBody = JsonConvert.SerializeObject(new Dictionary<string, string>
                {
                    { "scene_id", id },
                    { "params", @params }
                });
                
                _logger.LogInformation("GetData url: {url}, requestBody: {requestBody}", url, requestBody);

                var response = await _apiService.PostAsync(url, requestBody, cancellationToken);
                var result = JsonConvert.DeserializeObject<dynamic>(response);
                if (result != null && result.success == true)
                {
                    Dictionary<string, object> data = result.data.ToObject<Dictionary<string, object>>();
                    _logger.LogError("GetData接口成功. {data}", data);

                    await _eventHelper.DataEvent(JsonConvert.SerializeObject(new
                    {
                        type = "source",
                        data = new[]
                        {
                            new
                            {
                                type = "preview",
                                name = data["source_title"],
                                url = data["source_url"]
                            }
                        }
                    }), cancellationToken);

                    await _eventHelper.ToolEvent(title, name, @params, JsonConvert.SerializeObject(data), cancellationToken);

                    data.Remove("source_title");
                    data.Remove("source_url");
                    return JsonConvert.SerializeObject(data);
                }

                _logger.LogError("GetData接口失败，响应: {response}", response);
                await _eventHelper.ToolEvent(title, name, @params, response, cancellationToken);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "GetData接口异常");
                await _eventHelper.ToolEvent(title, name, @params, $"[GetData接口异常: {e}]", cancellationToken);
            }

            return "FAILED";
        }
    }
}