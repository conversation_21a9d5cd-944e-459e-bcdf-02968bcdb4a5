using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    public class AgentSkillEventHelper
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ChatRunDto _chatRunDto;

        public AgentSkillEventHelper(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto)) ?? throw new ArgumentNullException(nameof(ChatRunDto));
        }

        public async Task DataEvent(string content, CancellationToken cancellationToken)
        {
            var bits = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.DataEvent, content));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bits, cancellationToken);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync(cancellationToken);
        }

        public async Task TextEvent(string content, CancellationToken cancellationToken)
        {
            content = content.Replace("\n", "|n");
            // Console.WriteLine("----PluginBase.TextEvent:isStream:{0},content:{1}", isStream, content);
            var bits = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.TextEvent, content));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bits, cancellationToken);
            await Task.Delay(MysoftConstant.ChatDelay, cancellationToken);
        }

        public async Task ProcessEvent(string process, CancellationToken cancellationToken)
        {
            var bits = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ProcessEvent, $"{{\"text\": \"{process}\" }}"));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bits, cancellationToken);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync(cancellationToken);
        }

        public async Task ToolEvent(string title, string name, string arguments, string result, CancellationToken cancellationToken)
        {
            var data = new
            {
                type = "tool",
                tool = new
                {
                    title,
                    name,
                    arguments,
                    result
                }
            };

            var bits = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ProcessEvent, JsonConvert.SerializeObject(data)));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bits, cancellationToken);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync(cancellationToken);
        }

        public async Task ReasoningEvent(string content, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(content)) return;

            content = content.Replace("\n", "|n");
            var bits = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ReasoningEvent, content));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bits, cancellationToken);
            await Task.Delay(MysoftConstant.ChatDelay, cancellationToken);
        }
    }
}