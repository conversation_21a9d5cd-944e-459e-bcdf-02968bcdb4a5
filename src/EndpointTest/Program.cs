using System.Reflection;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;

Console.WriteLine("=== Endpoint兼容性测试 ===\n");

// 测试阿里云endpoint兼容处理
TestAliyunEndpoints();

Console.WriteLine("\n=== 测试完成 ===");

/// <summary>
/// 测试阿里云endpoint兼容处理
/// </summary>
static void TestAliyunEndpoints()
{
    Console.WriteLine("1. 测试阿里云endpoint兼容处理:");

    var testCases = new[]
    {
        ("https://dashscope.aliyuncs.com", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        ("http://dashscope.aliyuncs.com", "http://dashscope.aliyuncs.com/compatible-mode/v1"),
        ("https://dashscope.aliyuncs.com/", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        ("https://dashscope.aliyuncs.com/some/path", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        ("https://dashscope.aliyuncs.com/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        ("  https://dashscope.aliyuncs.com  ", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        ("https://custom.domain.com", "https://custom.domain.com/compatible-mode/v1")
    };

    foreach (var (input, expected) in testCases)
    {
        try
        {
            var modelInstance = new ModelInstanceDto
            {
                Endpoint = input,
                ModelType = ModelTypeEnum.Ali
            };

            var result = InvokeGetOpenAICompatibleEndpoint(modelInstance);
            var success = result?.ToString() == expected;

            Console.WriteLine($"  输入: {input}");
            Console.WriteLine($"  期望: {expected}");
            Console.WriteLine($"  实际: {result}");
            Console.WriteLine($"  结果: {(success ? "✓ 通过" : "✗ 失败")}\n");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  输入: {input}");
            Console.WriteLine($"  错误: {ex.Message}");
            Console.WriteLine($"  结果: ✗ 异常\n");
        }
    }
}

/// <summary>
/// 使用反射调用私有方法GetOpenAICompatibleEndpoint
/// </summary>
static Uri? InvokeGetOpenAICompatibleEndpoint(ModelInstanceDto modelInstance)
{
    var type = typeof(KernelBuilderExtensions);
    var method = type.GetMethod("GetOpenAICompatibleEndpoint", BindingFlags.NonPublic | BindingFlags.Static);

    if (method == null)
        throw new InvalidOperationException("GetOpenAICompatibleEndpoint method not found");

    try
    {
        return (Uri?)method.Invoke(null, new object[] { modelInstance });
    }
    catch (TargetInvocationException ex)
    {
        // 重新抛出内部异常
        throw ex.InnerException ?? ex;
    }
}
