using System;
using System.Reflection;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;

namespace Mysoft.GPTEngine.Test
{
    /// <summary>
    /// Endpoint兼容性测试程序
    /// </summary>
    class EndpointCompatibilityTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Endpoint兼容性测试 ===\n");

            // 测试阿里云endpoint兼容处理
            TestAliyunEndpoints();

            // 测试其他模型类型的endpoint检测
            TestModelTypeDetection();

            // 测试错误配置的修复
            TestMisconfiguredEndpoints();

            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试阿里云endpoint兼容处理
        /// </summary>
        static void TestAliyunEndpoints()
        {
            Console.WriteLine("1. 测试阿里云endpoint兼容处理:");

            var testCases = new[]
            {
                ("https://dashscope.aliyuncs.com", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                ("http://dashscope.aliyuncs.com", "http://dashscope.aliyuncs.com/compatible-mode/v1"),
                ("https://dashscope.aliyuncs.com/", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                ("https://dashscope.aliyuncs.com/some/path", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                ("https://dashscope.aliyuncs.com/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                ("  https://dashscope.aliyuncs.com  ", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                ("https://custom.domain.com", "https://custom.domain.com/compatible-mode/v1")
            };

            foreach (var (input, expected) in testCases)
            {
                try
                {
                    var modelInstance = new ModelInstanceDto
                    {
                        Endpoint = input,
                        ModelType = ModelTypeEnum.Ali
                    };

                    var result = InvokeGetOpenAICompatibleEndpoint(modelInstance);
                    var success = result?.ToString() == expected;
                    
                    Console.WriteLine($"  输入: {input}");
                    Console.WriteLine($"  期望: {expected}");
                    Console.WriteLine($"  实际: {result}");
                    Console.WriteLine($"  结果: {(success ? "✓ 通过" : "✗ 失败")}\n");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  输入: {input}");
                    Console.WriteLine($"  错误: {ex.Message}");
                    Console.WriteLine($"  结果: ✗ 异常\n");
                }
            }
        }

        /// <summary>
        /// 测试模型类型检测
        /// </summary>
        static void TestModelTypeDetection()
        {
            Console.WriteLine("2. 测试模型类型智能检测:");

            var testCases = new[]
            {
                ("https://dashscope.aliyuncs.com", ModelTypeEnum.OpenAI, ModelTypeEnum.Ali),
                ("https://api.openai.com", ModelTypeEnum.Ali, ModelTypeEnum.OpenAI),
                ("https://kimi.moonshot.cn", ModelTypeEnum.Ali, ModelTypeEnum.Kimi),
                ("https://spark.xunfei.cn", ModelTypeEnum.Ali, ModelTypeEnum.Xunfei),
                ("https://wenxin.baidu.com", ModelTypeEnum.Ali, ModelTypeEnum.Baidu)
            };

            foreach (var (endpoint, configuredType, expectedDetectedType) in testCases)
            {
                try
                {
                    var modelInstance = new ModelInstanceDto
                    {
                        Endpoint = endpoint,
                        ModelType = configuredType
                    };

                    var result = InvokeGetOpenAICompatibleEndpoint(modelInstance);
                    
                    Console.WriteLine($"  Endpoint: {endpoint}");
                    Console.WriteLine($"  配置类型: {configuredType}");
                    Console.WriteLine($"  期望检测: {expectedDetectedType}");
                    Console.WriteLine($"  处理结果: {result}");
                    Console.WriteLine($"  结果: ✓ 已处理\n");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Endpoint: {endpoint}");
                    Console.WriteLine($"  错误: {ex.Message}");
                    Console.WriteLine($"  结果: ✗ 异常\n");
                }
            }
        }

        /// <summary>
        /// 测试错误配置的修复
        /// </summary>
        static void TestMisconfiguredEndpoints()
        {
            Console.WriteLine("3. 测试错误配置的修复:");

            var testCases = new[]
            {
                // 阿里云endpoint配置为其他类型
                ("https://dashscope.aliyuncs.com", ModelTypeEnum.OpenAI),
                ("https://dashscope.aliyuncs.com", ModelTypeEnum.Kimi),
                
                // 其他模型endpoint配置错误
                ("https://api.openai.com", ModelTypeEnum.Ali),
                ("https://kimi.moonshot.cn", ModelTypeEnum.Baidu)
            };

            foreach (var (endpoint, wrongType) in testCases)
            {
                try
                {
                    var modelInstance = new ModelInstanceDto
                    {
                        Endpoint = endpoint,
                        ModelType = wrongType
                    };

                    var result = InvokeGetOpenAICompatibleEndpoint(modelInstance);
                    
                    Console.WriteLine($"  Endpoint: {endpoint}");
                    Console.WriteLine($"  错误配置类型: {wrongType}");
                    Console.WriteLine($"  修复后结果: {result}");
                    Console.WriteLine($"  结果: ✓ 已修复\n");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Endpoint: {endpoint}");
                    Console.WriteLine($"  错误: {ex.Message}");
                    Console.WriteLine($"  结果: ✗ 异常\n");
                }
            }
        }

        /// <summary>
        /// 使用反射调用私有方法GetOpenAICompatibleEndpoint
        /// </summary>
        private static Uri InvokeGetOpenAICompatibleEndpoint(ModelInstanceDto modelInstance)
        {
            var type = typeof(KernelBuilderExtensions);
            var method = type.GetMethod("GetOpenAICompatibleEndpoint", BindingFlags.NonPublic | BindingFlags.Static);
            
            if (method == null)
                throw new InvalidOperationException("GetOpenAICompatibleEndpoint method not found");

            try
            {
                return (Uri)method.Invoke(null, new object[] { modelInstance });
            }
            catch (TargetInvocationException ex)
            {
                // 重新抛出内部异常
                throw ex.InnerException ?? ex;
            }
        }
    }
}
