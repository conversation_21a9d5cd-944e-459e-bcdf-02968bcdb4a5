using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Application.ApprovalDatasource;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Repositories.Approval;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.Plugin.Activities;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using Serilog.Context;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Mysoft.GPTEngine.Domain
{
    public class ApprovalDomainService : DomainServiceBase
    {
        private readonly ILogger<ApprovalDomainService> _logger;
        private readonly PlanRuleInstanceRepostory _planRuleInstanceRepostory;
        private readonly PromptParamRepostory _promptParamRepostory;
        private readonly PromptRepostory _promptRepostory;
        private readonly PromptTemplateRepostory _promptTemplateRepostory;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly Kernel _kernel;
        private readonly ChatMessageNodeLogEntityRepostory _chatMessageNodeLogEntityRepostory;
        private readonly ApprovalDatasourceService _approvalDatasourceService;
        private readonly SemanticKernelActivity _semanticKernelActivity;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TActivity> _tlogger;

        private readonly Guid _reviewPrompt = Guid.Parse("08dcedac-0f98-455b-8e4e-b0ab6af3f906");

        public static readonly Guid SmartPromptGuid = Guid.Parse("f853704c-50b7-473c-abaa-89f0773c8af5");

        private readonly Dictionary<int, Guid> _smartPromptMap = new Dictionary<int, Guid>
        {
            { (int)RuleCheckSourceEnum.Form, SmartPromptGuid },
            { (int)RuleCheckSourceEnum.Attachments, SmartPromptGuid }
        };

        private readonly Dictionary<int, Guid> _rulePromptMap = new Dictionary<int, Guid>
        {
            { (int)RuleCheckSourceEnum.Form, Guid.Parse("08dcdc6b-860c-4373-8a60-e43749212ce7") },
            { (int)RuleCheckSourceEnum.Attachments, Guid.Parse("1398385c-b695-4986-bfaa-bb0165573aa6") }
        };

        public ApprovalDomainService(IMysoftContextFactory mysoftContextFactory,
            MysoftConfigurationDomain mysoftConfigurationDomain, IHttpContextAccessor httpContextAccessor,
            IMapper mapper, Kernel kernel
            , PromptParamRepostory promptParamRepostory, PlanRuleInstanceRepostory planRuleInstanceRepostory,
            IKnowledgeDomainService knowledgeDomainService
            , PromptRepostory promptRepostory, ILogger<ApprovalDomainService> logger,
            ChatMessageNodeLogEntityRepostory chatMessageNodeLogEntityRepostory
            , ApprovalDatasourceService approvalDatasourceService, SemanticKernelActivity semanticKernelActivity
            , PromptTemplateRepostory promptTemplateRepostory
            , IServiceProvider serviceProvider
            , ILogger<TActivity> tlogger) : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _promptRepostory = promptRepostory;
            _promptParamRepostory = promptParamRepostory;
            _promptTemplateRepostory = promptTemplateRepostory;
            _logger = logger;
            _planRuleInstanceRepostory = planRuleInstanceRepostory;
            _knowledgeDomainService = knowledgeDomainService;
            _kernel = kernel;
            _chatMessageNodeLogEntityRepostory = chatMessageNodeLogEntityRepostory;
            _approvalDatasourceService = approvalDatasourceService;
            _semanticKernelActivity = semanticKernelActivity;
            _mysoftConfigurationDomain = mysoftConfigurationDomain;
            _serviceProvider = serviceProvider;
            _tlogger = tlogger;
        }

        /// <summary>
        /// 执行审批方案
        /// </summary>
        /// <param name="executePlanJobDto"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task ExecutePlan(ExecutePlanJobDto executePlanJobDto, Kernel kernel)
        {
            // 先把数据源查询出来
            await LoadAndCacheDataSourcesAsync(executePlanJobDto);

            // 设置最大并发数为6
            var concurrencyLimit = 6;
            var semaphore = new SemaphoreSlim(concurrencyLimit);

            var tasks = new List<Task>();

            // 遍历规则组
            foreach (var group in executePlanJobDto.PlanInstanceInfoDto.RuleGroups)
            {
                foreach (var rule in group.Rules)
                {
                    // 捕获循环变量，防止闭包问题
                    var currentRule = rule;
                    await semaphore.WaitAsync(); // 等待信号量

                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            currentRule.Status = (int)ApprovalPlanStatusEnum.Running;
                            await UpdateRuleDto(currentRule, executePlanJobDto.PlanInstanceInfoDto);
                            await ExecuteRule(executePlanJobDto, currentRule, kernel);
                            currentRule.Status = (int)ApprovalPlanStatusEnum.Success;
                        }
                        catch (Exception ex)
                        {
                            HandleRuleExecutionException(rule, ex);
                        }
                        finally
                        {
                            await UpdateRuleDto(currentRule, executePlanJobDto.PlanInstanceInfoDto);
                            semaphore.Release(); // 释放信号量
                        }
                    }));
                }
            }
            await Task.WhenAll(tasks); // 等待所有任务完成
        }

        private async Task LoadAndCacheDataSourcesAsync(ExecutePlanJobDto executePlanJobDto)
        {
            // 先找到方案下所有的规则
            HashSet<string> planRuleDataInstanceGUIDS = executePlanJobDto.PlanInstanceInfoDto.RuleGroups
                .SelectMany(g => g.Rules)
                .Select(r => r.PlanRuleInstanceGUID)
                .ToHashSet();
            
            await _approvalDatasourceService.LoadAndCacheDataSourcesAsync(planRuleDataInstanceGUIDS, executePlanJobDto.PlanInstanceInfoDto);
        }

        private void HandleRuleExecutionException(RuleDto rule, Exception ex)
        {
            (string, string, string, int) errInfo = ErrorCodeInitHelper.ErrorCodeInit(ex);
            var logId = Guid.NewGuid().ToString().Replace("-", "");
            rule.Status = (int)ApprovalPlanStatusEnum.Fail;
            rule.ErrorMessage = logId;
            rule.UserMessage = errInfo.Item3 == "exception" ? "" : errInfo.Item2;

            if (errInfo.Item3 == "exception")
            {
                using (LogContext.PushProperty("LogId", logId))
                {
                    _logger.LogError(ex, "执行审批规则失败");
                }
            }
        }

        /// <summary>
        /// 执行审批规则
        /// </summary>
        /// <param name="executePlanJobDto"></param>
        /// <param name="rule"></param>
        /// <returns></returns>
        private async Task ExecuteRule(ExecutePlanJobDto executePlanJobDto, RuleDto rule, Kernel kernel)
        {
            if (rule.RuleType != (int)RuleTypeEnum.Ai)
            {
                rule.Status = (int)RuleStatusEnum.Fail;
                return;
            }

            if (rule.RuleCheckSource == (int)RuleCheckSourceEnum.Form)
            {
                await CheckFormData(rule, executePlanJobDto);
            }
            else if (rule.RuleCheckSource == (int)RuleCheckSourceEnum.Attachments)
            {
                List<AttachmentInfoDto> attachmentInfoList = new List<AttachmentInfoDto>();
                if (executePlanJobDto.PlanInstanceInfoDto.Attachments != null &&
                    executePlanJobDto.PlanInstanceInfoDto.Attachments.Count > 0)
                {
                    foreach (var item in executePlanJobDto.PlanInstanceInfoDto.Attachments)
                    {
                        attachmentInfoList.Add(new AttachmentInfoDto()
                        {
                            DocumentGuid = item.Id,
                            Name = item.Title
                        });
                    }
                }

                await CheckAttachments(rule, executePlanJobDto, attachmentInfoList, "");
            }
        }

        /// <summary>
        /// 执行审批插件-大模型请求
        /// </summary>
        /// <param name="promptGuid"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        private async Task<string> ExecuteApprovalPlugin(
            Guid promptGuid,
            ApprovalDataDto dto,
            ExecutePlanJobDto executePlanJobDto)
        {
            ChatMessageNodeLogDto nodeLog = GetNodeLog(executePlanJobDto, dto.Name);
            var promptDto = await RuleCheckDataProcess(promptGuid, JsonSerializer.Serialize(dto));

            var chatRunDto = new ChatRunDto
            {
                Stopwatch = Stopwatch.StartNew(),
                IsStream = false,
                NodeLogs = { nodeLog },
                Nodes =
                {
                    new FlowNode
                    {
                        Id = nodeLog.NodeGUID.ToString(),
                        Name = "执行审批插件",
                        Config = new NodeConfig { TemplateId = promptDto.Id.ToString() }
                    }
                },
                SkillOrchestration = new SkillOrchestrationDto
                {
                    Prompts = new List<PromptDto> { promptDto }
                },
                Chat = new ChatDto
                {
                    ChatGUID = nodeLog.ChatGUID,
                    CurrentNodeGUID = nodeLog.NodeGUID
                }
            };

            // 模型实例代码处理
            promptDto.ModelInstanceCode = !string.IsNullOrEmpty(executePlanJobDto.ModelInstanceCode)
                ? executePlanJobDto.ModelInstanceCode
                : ChatCompletionTypeDto.TextGeneration;

            chatRunDto.ModelInstance = new ModelInstanceDto
            {
                InstanceCode = promptDto.ModelInstanceCode
            };

            // 参数处理和大模型执行
            var arguments = new KernelArguments();
            promptDto.InputParam.ForEach(param =>
                arguments.Add(param.LiteralCode, param.LiteralValue));

            SemanticKernelActivity semanticKernelActivity = new SemanticKernelActivity(
                _serviceProvider,
                _tlogger);
            semanticKernelActivity._chatRunDto = chatRunDto;

            var flowNode = await chatRunDto.GetFlowNode(); // 移除阻塞调用
            var chatHistory = await semanticKernelActivity.CreateChatHistory(flowNode, promptDto, arguments);

            SaveInputMultiThread(flowNode, chatHistory, chatRunDto);
            // SaveInput(flowNode, chatHistory);

            flowNode._chatHistory = chatHistory;
            flowNode._serviceId = promptDto.ModelInstanceCode;

            var result = await semanticKernelActivity.ChatCompletionExec(
                flowNode,
                chatHistory,
                promptDto.ModelInstanceCode,
                promptDto.ExecutionSetting
            );

            // 日志记录和处理
            chatRunDto.Stopwatch.Stop();
            nodeLog.SetFirstRespDuration(chatRunDto.Stopwatch.ElapsedMilliseconds);
           
            await SaveNodeLogMultiThread(result, chatRunDto);
            // await SaveNodeLog(result);

            return result;
        }


        private void SaveInputMultiThread(FlowNode flowNode, ChatHistory chatHistory, ChatRunDto chatRunDto)
        {
            var nodeLog = chatRunDto.NodeLogs.FirstOrDefault(x => x.NodeGUID == Guid.Parse(flowNode.Id));
            if (nodeLog == null || chatHistory.Count == 0) return;
            nodeLog.Inputs =
                JsonConvert.SerializeObject(chatHistory.Count > 1 ? chatHistory[1].Content : chatHistory[0].Content);
        }
        
        private void SaveInput(FlowNode flowNode, ChatHistory chatHistory)
        {
            var chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));
            var nodeLog = chatRunDto.NodeLogs.FirstOrDefault(x => x.NodeGUID == Guid.Parse(flowNode.Id));
            if (nodeLog == null || chatHistory.Count == 0) return;
            nodeLog.Inputs =
                JsonConvert.SerializeObject(chatHistory.Count > 1 ? chatHistory[1].Content : chatHistory[0].Content);
        }


        private ChatMessageNodeLogDto GetNodeLog(ExecutePlanJobDto executePlanJobDto, string name)
        {
            PlanMessageNodeLogDto planMessageNodeLogDto = executePlanJobDto.PlanMessageNodeLogDto;
            return new ChatMessageNodeLogDto
            {
                ChatMessageNodeLogGUID = Guid.NewGuid(),
                BatchGUID = planMessageNodeLogDto.BatchGUID,
                ChatGUID = planMessageNodeLogDto.ChatGUID,
                NodeGUID = planMessageNodeLogDto.NodeGUID,
                Name = planMessageNodeLogDto.Name + "_" + name,
                Description = "",
                Index = planMessageNodeLogDto.Index,
                StartTime = TimeZoneUtility.LocalNow(),
                Config = "",
            };
        }

        private async Task SaveNodeLogMultiThread(String result, ChatRunDto chatRunDto)
        {
            var nodeLogs = _mapper.Map<List<ChatMessageNodeLogEntity>>(chatRunDto.NodeLogs);
            var nodeLog = nodeLogs.FirstOrDefault(x => x.NodeGUID == chatRunDto.Chat.CurrentNodeGUID);
            if (nodeLog == null) return;

            _logger.LogInformation("检查规则大模型返回：\n{0}", result);
            // 截取从 startIndex 到 endIndex 的字符串
            result = JsonValidateHelper.CleanUpJson(result);

            nodeLog.Outputs = result;
            nodeLog.EndTime = TimeZoneUtility.LocalNow();
            TimeSpan difference = nodeLog.EndTime - nodeLog.StartTime;
            nodeLog.Duration = difference.TotalSeconds;
            await _chatMessageNodeLogEntityRepostory.InsertManyAsync(nodeLogs);
        }

        private async Task SaveNodeLog(String result)
        {
            var chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));
            var nodeLogs = _mapper.Map<List<ChatMessageNodeLogEntity>>(chatRunDto.NodeLogs);
            var nodeLog = nodeLogs.FirstOrDefault(x => x.NodeGUID == chatRunDto.Chat.CurrentNodeGUID);
            if (nodeLog == null) return;

            _logger.LogInformation("检查规则大模型返回：\n{0}", result);
            // 截取从 startIndex 到 endIndex 的字符串
            result = JsonValidateHelper.CleanUpJson(result);

            nodeLog.Outputs = result;
            nodeLog.EndTime = TimeZoneUtility.LocalNow();
            TimeSpan difference = nodeLog.EndTime - nodeLog.StartTime;
            nodeLog.Duration = difference.TotalSeconds;
            await _chatMessageNodeLogEntityRepostory.InsertManyAsync(nodeLogs);
        }

        /// <summary>
        /// 审批结果反思
        /// </summary>
        private async Task<RuleDto> ExecuteReviewPrompt(string result, string rule, string name, Guid reviewPromptGuid,
            ExecutePlanJobDto executePlanJobDto)
        {
            var dto = new ApprovalDataDto() { Data = result, Rule = rule, Name = name + "_反思" };

            var reviewResult = await ExecuteApprovalPlugin(reviewPromptGuid, dto, executePlanJobDto);

            return await Task.FromResult(FormatResult(reviewResult));
        }

        public RuleDto FormatResult(string result)
        {
            if (JsonUtility.TryDeserializeJsonStringObjectList<List<ExcuteResultDto>>(result, out var excuteResultDtos))
            {
                RuleDto rule = new RuleDto();
                rule.Result = (int)ApprovalPlanConformityResultEnum.Conform;
                var resultList = excuteResultDtos.Where(dto => dto.Status != null).ToList();
                if (resultList.Any(dto => !dto.Status ?? true))
                {
                    rule.Result = (int)ApprovalPlanConformityResultEnum.NonConform;
                    rule.Details = string.Join("\r\n", resultList
                        .Where(dto => !dto.Status ?? true)
                        .Select(dto => "- " + dto.Message));
                }
                else
                {
                    rule.Result = (int)ApprovalPlanConformityResultEnum.Conform;
                }

                return rule;
            }

            _logger.LogError("执行审批规则失败，JSON序列化错误：" + result);
            throw new JsonFormatException(ErrMsgConst.JsonFormat_ErrMsg);
        }

        /// <summary>
        /// 表单数据审批
        /// </summary>
        private async Task CheckFormData(RuleDto rule, ExecutePlanJobDto executePlanJobDto)
        {
            PlanInstanceInfoDto planInstanceInfoDto = executePlanJobDto.PlanInstanceInfoDto;

            SchemaData data = await _approvalDatasourceService.GetData(rule, planInstanceInfoDto);

            if (data.AttachmentInfoList != null && data.AttachmentInfoList.Count > 0)
            {
                await CheckAttachments(rule, executePlanJobDto, data.AttachmentInfoList, data.Data);
                return;
            }

            var dto = new ApprovalDataDto()
            {
                Data = data.Data, Rule = rule.RuleCheckContent, ErrorDemo = rule.RuleCheckResultDemo,
                Name = rule.RuleName
            };
            RuleDto resultDto;
            if (planInstanceInfoDto.PromptMode == (int)PlanPromptModeEnum.Smart)
            {
                var promptGuid = _smartPromptMap[rule.RuleCheckSource];

                var result = await ExecuteApprovalPlugin(promptGuid, dto, executePlanJobDto);

                resultDto = FormatResult(result);
            }
            else
            {
                var promptGuid = _rulePromptMap[rule.RuleCheckSource];

                var result = await ExecuteApprovalPlugin(promptGuid, dto, executePlanJobDto);

                resultDto = await ExecuteReviewPrompt(result, rule.RuleCheckContent, rule.RuleName, _reviewPrompt,
                    executePlanJobDto);
            }


            rule.Result = resultDto.Result;
            rule.Overview = resultDto.Overview;
            rule.Details = resultDto.Details;
        }


        /// <summary>
        /// 附件数据审批
        /// </summary>
        private async Task CheckAttachments(RuleDto rule, ExecutePlanJobDto executePlanJobDto,
            List<AttachmentInfoDto> attachmentInfoList, String data)
        {
            PlanInstanceInfoDto planInstanceInfoDto = executePlanJobDto.PlanInstanceInfoDto;
            int lastResult = -1;
            string overview = "";
            string details = "";
            if (attachmentInfoList == null || attachmentInfoList.Count == 0)
            {
                lastResult = 0;
                overview = "附件信息为空无法进行审核";
                details = "附件信息为空无法进行审核";
            }

            foreach (var attachment in attachmentInfoList)
            {
                if (string.IsNullOrEmpty(attachment.DocumentGuid))
                {
                    lastResult = 0;
                    overview += String.Format("{0}: \n 附件ID为空无法进行审核。 \n", attachment.Name);
                    details += String.Format("{0}: \n 附件ID为空无法进行审核。 \n", attachment.Name);
                    continue;
                }

                string contnent = await GetDocumentContent(attachment.DocumentGuid);
                if (string.IsNullOrEmpty(contnent))
                {
                    lastResult = 0;
                    overview += String.Format("{0}: \n 未读取到附件内容。 \n", attachment.Name);
                    details += String.Format("{0}: \n 未读取到附件内容。 \n", attachment.Name);
                    continue;
                }

                var dto = new ApprovalDataDto()
                {
                    Data = data, BusinessFiles = contnent, Rule = rule.RuleCheckContent,
                    ErrorDemo = rule.RuleCheckResultDemo, Name = rule.RuleName
                };

                RuleDto resultDto;
                if (planInstanceInfoDto.PromptMode == (int)PlanPromptModeEnum.Smart)
                {
                    var promptGuid = _smartPromptMap[rule.RuleCheckSource];

                    var result = await ExecuteApprovalPlugin(promptGuid, dto, executePlanJobDto);

                    resultDto = FormatResult(result);
                }
                else
                {
                    var promptGuid = _rulePromptMap[rule.RuleCheckSource];
                    var result = await ExecuteApprovalPlugin(promptGuid, dto, executePlanJobDto);
                    resultDto = await ExecuteReviewPrompt(result, rule.RuleCheckContent, rule.RuleName, _reviewPrompt,
                        executePlanJobDto);
                }

                if (resultDto.Result == (int)ApprovalPlanConformityResultEnum.NonConform)
                {
                    lastResult = resultDto.Result;
                    details += resultDto.Details;
                    overview += resultDto.Overview;
                }
                else if (resultDto.Result == (int)ApprovalPlanConformityResultEnum.Unknown)
                {
                    if (lastResult != (int)ApprovalPlanConformityResultEnum.NonConform)
                    {
                        lastResult = resultDto.Result;
                    }

                    details += resultDto.Details;
                    overview += resultDto.Overview;
                }
                else if (resultDto.Result == (int)ApprovalPlanConformityResultEnum.Conform &&
                         (lastResult == (int)ApprovalPlanConformityResultEnum.Conform || lastResult == -1))
                {
                    lastResult = resultDto.Result;
                }
            }

            rule.Result = lastResult;
            rule.Overview = overview;
            rule.Details = details;
        }

        /// <summary>
        /// 组装会话数据
        /// </summary>
        /// <param name="promptGuid"></param>
        /// <param name="paramsStr"></param>
        /// <returns></returns>
        private async Task<PromptDto> RuleCheckDataProcess(Guid promptGuid, string paramsStr)
        {
            var prompt = await _promptRepostory.GetAsync(x => x.PromptGUID == promptGuid);
            var promptTemplate = await _promptTemplateRepostory.GetAsync(x => x.PromptGUID == promptGuid);
            var promptParams = await _promptParamRepostory.GetListAsync(x => x.PromptGUID == promptGuid);
            var promptDto = new PromptDto
            {
                Id = promptGuid,
                PromptTemplate = promptTemplate.PromptTemplate,
                ModelInstanceCode = prompt.ModelInstanceCode,
                ExecutionSetting = prompt.ExecutionSetting,
                OutputType = prompt.OutputType,
                InputParam = promptParams?.Where(x => x.ParamType == ParamTypeEnum.Input)?.Select(x => new ParamDto
                {
                    Code = x.ParamCode,
                    Name = x.ParamName,
                    Required = x.IsRequired,
                    Type = x.FiledType,
                    Description = x.Describe,
                    DefaultValue = x.DefaultValue,
                    LiteralCode = x.ParamCode,
                    LiteralValue = JsonUtility.TryGetPropertyValue(paramsStr, x.ParamCode)
                })?.ToList()
            };
            if (prompt.Mode == 1 && !String.IsNullOrEmpty(promptTemplate.MessageContent))
            {
                promptDto.MessageContents = JsonConvert
                    .DeserializeObject<List<MessageContent>>(promptTemplate.MessageContent).OrderBy(x => x.Index)
                    .ToList();
            }

            return await Task.FromResult(promptDto);
        }

        /// <summary>
        /// 读取文档数据
        /// </summary>
        private async Task<string> GetDocumentContent(string id)
        {
            List<DocumentInfoBaseDto> documentInfoBaseDtos =
                await _knowledgeDomainService.GetDocumentInfo(new List<Guid>() { Guid.Parse(id) });

            if (documentInfoBaseDtos == null || documentInfoBaseDtos.Count <= 0) return await Task.FromResult("");

            string downloadUrl = documentInfoBaseDtos[0].DownloadUrl;
            string fileName = documentInfoBaseDtos[0].FileName;
            string type = FileTypeConvertHelper.GetFileType(fileName);
            IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type, _kernel,
                _mysoftApiDomainService, _mysoftContextFactory, _httpContextAccessor, _mapper);
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                MemoryStream stream = new MemoryStream(fileBytes);
                NodeConfig nodeConfig = new NodeConfig();
                nodeConfig.ImageRecognizeType = "ocr";
                var md = await documentDecoder.GetDocumentTxt(stream, nodeConfig);
                return await Task.FromResult(md);
            }
        }

        /// <summary>
        /// 修改规则示例状态
        /// </summary>
        public async Task UpdateRuleDto(RuleDto rule, PlanInstanceInfoDto planInstanceInfoDto)
        {
            if (planInstanceInfoDto.PlanMode == (int)ApprovalPlanModeEnum.RiskLevel)
            {
                if (rule.Result == (int)ApprovalPlanConformityResultEnum.Conform)
                {
                    rule.Result = (int)ApprovalPlanRiskLevelResultEnum.Pass;
                }
                else if (rule.Result == (int)ApprovalPlanConformityResultEnum.NonConform)
                {
                    rule.Result = rule.RiskLevel;
                }
            }

            await _planRuleInstanceRepostory.UpdateAsync(e => new PlanRuleInstanceEntity
            {
                Status = rule.Status,
                ErrorMessage = rule.ErrorMessage,
                UserMessage = rule.UserMessage,
                Details = rule.Details,
                Overview = rule.Overview,
                Result = rule.Result,
                ModifiedTime = TimeZoneUtility.LocalNow()
            }, f => f.PlanRuleInstanceGUID == rule.PlanRuleInstanceGUID);
        }
    }
}