# TruncateToolName 截断工具功能修复

## 问题描述

在 Semantic Kernel 中，当使用 `kernel.Plugins.Add()` 或 `AddFromFunctions()` 添加插件时，最终的工具调用名称格式是 `PluginName-FunctionName`（使用连字符分隔）。

原有的 `TruncateToolName` 函数只考虑了函数名的长度限制（64字符），但没有考虑插件名的长度。实际上，完整的工具名称是 `PluginName + "-" + FunctionName`，所以需要确保整个组合不超过64字符。

## 修复内容

### 1. 创建统一的工具名称处理类

创建了 `src/Mysoft.GPTEngine.Domain/Extensions/ToolNameHelper.cs`，提供统一的截断逻辑：

- `TruncateFunctionName(functionName, pluginName, maxTotalLength)` - 新方法，考虑插件名长度
- `TruncateToolName(toolName, maxLength)` - 旧方法，标记为过时，保持向后兼容
- `SmartTruncate()` - 智能截断算法，保留重要部分

### 2. 修复的文件

#### 2.1 AgentKnowledgePlugin.cs
- 添加 `using Mysoft.GPTEngine.Domain.Extensions;`
- 将 `TruncateToolName(functionName)` 改为 `ToolNameHelper.TruncateFunctionName(functionName, "KnowledgeTools")`
- 删除了本地的 `TruncateToolName` 方法

#### 2.2 DataKnowledgeImporter.cs
- 添加 `using Mysoft.GPTEngine.Domain.Extensions;`
- 将 `TruncateToolName(name)` 改为 `ToolNameHelper.TruncateFunctionName(name, "DataKnowledgeTools")`
- 删除了本地的 `TruncateToolName` 方法

#### 2.3 DataPlatformToolsImporter.cs
- 添加 `using Mysoft.GPTEngine.Domain.Extensions;`
- 将 `TruncateToolName(name)` 改为 `ToolNameHelper.TruncateFunctionName(name, "GetDataTools")`
- 删除了本地的 `TruncateToolName` 方法

#### 2.4 OpenApiKernelExtensions.cs
- 添加 `using Mysoft.GPTEngine.Domain.Extensions;`
- 修改 `ConvertOperationIdToValidFunctionName` 方法签名，添加 `pluginName` 参数
- 使用 `ToolNameHelper.TruncateFunctionName(result, pluginName)` 替代原有的截断逻辑

### 3. 保留的正确实现

`McpCustomService.cs` 中的 `TruncateToolName` 实现已经是正确的，因为它已经考虑了插件名长度：

```csharp
var maxFunctionNameLength = maxTotalLength - _pluginName.Length - separatorLength;
```

所以保留了这个实现不变。

## 技术细节

### 工具名称格式

在 Semantic Kernel 中，完整的工具名称格式为：
```
PluginName-FunctionName
```

例如：
- `KnowledgeTools-QueryProductKnowledge`
- `GetDataTools-GetUserData`
- `McpTools-SearchFiles`

### 长度限制

- 总长度限制：64字符
- 分隔符长度：1字符（"-"）
- 函数名最小长度：3字符

### 智能截断策略

1. **策略1**：如果包含分隔符（`_`, `-`, `.`, `/`），保留最后几个有意义的部分
2. **策略2**：如果是驼峰命名，保留重要的单词（动词+名词）
3. **策略3**：简单截断并添加哈希后缀确保唯一性

## 验证

编译项目成功，没有编译错误：
```bash
dotnet build src/Mysoft.GPTEngine.sln
```

## 影响范围

此修复影响以下功能：
- 知识库工具导入
- 数据知识库工具导入
- 数据平台工具导入
- OpenAPI 插件导入
- MCP 工具导入（已经是正确的）

## 向后兼容性

- 保留了旧的 `TruncateToolName` 方法，标记为 `[Obsolete]`
- 现有代码可以继续工作，但会收到过时警告
- 建议逐步迁移到新的 `TruncateFunctionName` 方法

## 总结

这个修复确保了所有工具名称截断逻辑都正确考虑了插件名的长度，避免了工具调用时因名称过长而导致的问题。同时提供了统一的处理逻辑，便于维护和扩展。
